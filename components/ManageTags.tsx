import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { db } from "@/db";
import { categories } from "@/db/schema";
import { eq } from "drizzle-orm";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Plus, X } from "lucide-react";
import { Badge } from "./ui/badge";

interface ManageTagsProps {
  type: "Category";
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

type Tag = {
  id: string;
  name: string;
};

const ManageTags = ({ type, open, onOpenChange }: ManageTagsProps) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const queryKey = ["categories"];

  const [newItem, setNewItem] = useState("");

  const { data: items = [], isLoading } = useQuery<Tag[]>({
    queryKey,
    queryFn: async () => {
      const result = await db
        .select({ id: categories.id, name: categories.name })
        .from(categories)
        .orderBy(categories.name);
      return result;
    },
  });

  const { mutate: addItem, isPending: isAdding } = useMutation({
    mutationFn: async (name: string) => {
      const result = await db.insert(categories).values({ name }).returning();
      return result;
    },
    onSuccess: () => {
      toast({ title: `${type} added` });
      queryClient.invalidateQueries({ queryKey });
      setNewItem("");
    },
    onError: (error: Error) => {
      toast({
        title: `Error adding ${type}`,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const { mutate: deleteItem } = useMutation({
    mutationFn: async (id: string) => {
      await db.delete(categories).where(eq(categories.id, id));
    },
    onSuccess: () => {
      toast({ title: `${type} deleted` });
      queryClient.invalidateQueries({ queryKey });
    },
    onError: (error: Error) => {
      toast({
        title: `Error deleting ${type}`,
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newItem.trim()) {
      addItem(newItem.trim());
    }
  };

  const handleDelete = (id: string) => {
    deleteItem(id);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Manage {type}s</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex space-x-2">
            <Input
              value={newItem}
              onChange={(e) => setNewItem(e.target.value)}
              placeholder={`Add new ${type.toLowerCase()}`}
              className="flex-1"
            />
            <Button type="submit" disabled={isAdding || !newItem.trim()}>
              {isAdding ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Plus className="h-4 w-4" />
              )}
            </Button>
          </div>
        </form>

        <div className="space-y-2">
          <Label className="text-sm font-medium">Existing {type}s</Label>
          {isLoading ? (
            <div className="flex items-center justify-center py-4">
              <Loader2 className="h-4 w-4 animate-spin" />
            </div>
          ) : items.length === 0 ? (
            <p className="text-sm text-muted-foreground text-center py-4">
              No {type.toLowerCase()}s found
            </p>
          ) : (
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {items.map((item) => (
                <div
                  key={item.id}
                  className="flex items-center justify-between p-2 border rounded-md"
                >
                  <Badge variant="secondary">{item.name}</Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDelete(item.id)}
                    className="text-destructive hover:text-destructive"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ManageTags;
