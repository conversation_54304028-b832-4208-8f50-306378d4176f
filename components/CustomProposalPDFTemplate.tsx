import React from "react";
import { Proposal, ProposalTemplateSettings } from "../types/proposal";

interface CustomProposalPDFTemplateProps {
  proposal: Proposal;
  templateSettings: ProposalTemplateSettings | null | undefined;
}

export const CustomProposalPDFTemplate: React.FC<
  CustomProposalPDFTemplateProps
> = ({ proposal, templateSettings }) => {
  const settings = {
    cover_page_title: templateSettings?.cover_page_title || "Speaker Proposal",
    cover_page_image_url:
      templateSettings?.cover_page_image_url || "/images/background.jpeg",
    about_us_mission:
      templateSettings?.about_us_mission ||
      "At MENA Speakers, we connect world-class speakers with organizations seeking exceptional thought leadership and inspiration. Our mission is to deliver transformative experiences that educate, motivate, and drive meaningful change.",
  };

  const styles: { [key: string]: React.CSSProperties } = {
    page: {
      width: "210mm",
      minHeight: "297mm",
      padding: "20mm",
      margin: "0 auto",
      boxSizing: "border-box",
      display: "flex",
      flexDirection: "column",
      backgroundColor: "white",
      fontFamily: "sans-serif",
      color: "#333",
    },
    coverPage: {
      justifyContent: "center",
      alignItems: "center",
      textAlign: "center",
      backgroundColor: "#000",
      color: "white",
      position: "relative",
      overflow: "hidden",
    },
    coverGrid: {
      display: "grid",
      gridTemplateColumns: "repeat(4, 1fr)",
      gridTemplateRows: "repeat(3, 1fr)",
      gap: "8px",
      width: "100%",
      height: "100%",
      position: "absolute",
      top: 0,
      left: 0,
      padding: "20mm",
      boxSizing: "border-box",
    },
    gridPanel: {
      backgroundColor: "rgba(255, 255, 255, 0.1)",
      borderRadius: "8px",
      border: "1px solid rgba(255, 255, 255, 0.2)",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      fontSize: "10px",
      color: "rgba(255, 255, 255, 0.8)",
      backdropFilter: "blur(5px)",
    },
    logoSection: {
      position: "absolute",
      top: "50%",
      left: "50%",
      transform: "translate(-50%, -50%)",
      textAlign: "center",
      zIndex: 10,
      backgroundColor: "rgba(0, 0, 0, 0.7)",
      padding: "40px",
      borderRadius: "20px",
      border: "2px solid rgba(255, 255, 255, 0.3)",
      backdropFilter: "blur(10px)",
    },
    logo: {
      width: "auto",
      height: "auto",
      margin: "0 auto 20px",
      filter: "brightness(0) invert(1)",
    },
    mainTitle: {
      fontSize: "48px",
      fontWeight: "bold",
      margin: "0 0 10px 0",
      color: "white",
      textShadow: "2px 2px 4px rgba(0, 0, 0, 0.5)",
    },
    subtitle: {
      fontSize: "18px",
      margin: "0 0 20px 0",
      color: "rgba(255, 255, 255, 0.9)",
      fontWeight: "normal",
    },
    tagline: {
      fontSize: "14px",
      color: "rgba(255, 255, 255, 0.8)",
      fontStyle: "italic",
    },
    h1: { fontSize: "48px", margin: "0 0 20px 0" },
    h2: {
      fontSize: "32px",
      margin: "40px 0 20px 0",
      borderBottom: "2px solid #333",
      paddingBottom: "10px",
    },
    h3: { fontSize: "24px", margin: "20px 0 10px 0" },
    p: { fontSize: "16px", lineHeight: "1.6" },
    speakerCard: {
      border: "1px solid #eee",
      padding: "15px",
      borderRadius: "8px",
      marginBottom: "20px",
      display: "flex",
      gap: "20px",
    },
    speakerImage: {
      width: "100px",
      height: "100px",
      borderRadius: "50%",
      objectFit: "cover",
    },
    total: {
      marginTop: "auto",
      paddingTop: "20px",
      borderTop: "2px solid #333",
      textAlign: "right",
      fontSize: "28px",
      fontWeight: "bold",
    },
  };

  return (
    <html>
      <head>
        <meta charSet="utf-8" />
        <title>{`Proposal for ${proposal.clientName}`}</title>
      </head>
      <body>
        {/* Cover Page with MENA Speakers Collage Design */}
        <div
          className="pdf-page"
          style={{
            ...styles.page,
            ...styles.coverPage,
            padding: 0,
            backgroundImage: `url(${settings.cover_page_image_url})`,
            backgroundSize: "contain",
            backgroundRepeat: "no-repeat",
            backgroundPosition: "center",
          }}
        >
          {/* Central Logo and Branding */}
          <div style={styles.logoSection}>
            <img
              src="/images/logo.png"
              alt="MENA Speakers Logo"
              style={styles.logo}
            />
            <h1 style={styles.mainTitle}>MENA speakers</h1>
            <p style={styles.subtitle}>
              The #1 Speakers Bureau in the MENA Region since 2016
            </p>
            <p style={styles.tagline}>
              Professional • Exclusive • Empowering • Trustworthy
            </p>
          </div>

          {/* Proposal Details Overlay */}
          <div
            style={{
              position: "absolute",
              bottom: "40px",
              right: "40px",
              backgroundColor: "rgba(0, 0, 0, 0.8)",
              padding: "20px",
              borderRadius: "10px",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              textAlign: "left",
              maxWidth: "300px",
            }}
          >
            <h3
              style={{ fontSize: "18px", margin: "0 0 15px 0", color: "white" }}
            >
              {settings.cover_page_title}
            </h3>
            <p
              style={{
                fontSize: "14px",
                margin: "0 0 8px 0",
                color: "rgba(255, 255, 255, 0.9)",
              }}
            >
              <strong>Client:</strong> {proposal.clientName}
            </p>
            <p
              style={{
                fontSize: "14px",
                margin: "0 0 8px 0",
                color: "rgba(255, 255, 255, 0.9)",
              }}
            >
              <strong>Event:</strong> {proposal.event.eventName}
            </p>
            <p
              style={{
                fontSize: "14px",
                margin: "0 0 8px 0",
                color: "rgba(255, 255, 255, 0.9)",
              }}
            >
              <strong>Date:</strong>{" "}
              {new Date(proposal.createdAt).toLocaleDateString()}
            </p>
            <p
              style={{
                fontSize: "12px",
                margin: "0",
                color: "rgba(255, 255, 255, 0.7)",
                fontStyle: "italic",
              }}
            >
              Prepared with excellence for your success
            </p>
          </div>
        </div>

        <div className="pdf-page" style={styles.page}>
          <h2 style={styles.h2}>About Us</h2>
          <div
            style={{ display: "flex", gap: "20px", alignItems: "flex-start" }}
          >
            <div style={{ flex: 1 }}>
              <p style={styles.p}>
                MENA Speakers is a highly acclaimed agency offering customized
                solutions for various speaking engagements. Established in 2016,
                we have become the foremost speakers' agency in the Middle East,
                offering services that can compete with international agencies.
                Our exceptional track record is a testament to our unwavering
                commitment to delivering top notch speakers who constantly raise
                the bar.
              </p>
              <p style={styles.p}>
                Our expert orators can innovate, facilitate, moderate, and
                motivate audiences across different events, including keynote
                speeches, educational seminars, and one-on-one meetings. Under
                the leadership of Saana Azzam, an award-winning economist, MENA
                Speakers represents the best and brightest in the region,
                including VIPs such as Prince Salman from Saudi Arabia, Mohammed
                Qahtani, world champion in public speaking, and Muna
                AbuSulayman.
              </p>
              <p style={styles.p}>
                We have also brought in renowned international speakers such as
                Fed Reserve Chairman Ben Bernanke, Janet Yellen, and David
                Meltzer. Our exclusive speaker roster includes Mathew Knowles,
                the father and agent of Beyoncé, Joe Foster, the founder of
                Reebok, and Hala Gorani, a CNN anchor. We have an impressive
                client base, which includes Gulf Ministries, Kellogg's Company,
                Standard Chartered Bank, NEOM, Misk, Al Ula, and many more.
              </p>
              <p style={styles.p}>
                In short, MENA Speakers is a trusted and reliable agency that
                provides exceptional speakers and services to meet the diverse
                needs of our clients.
              </p>
            </div>
            <div style={{ width: "35%" }}>
              <img
                src="/images/about-us-side-image.png"
                alt="About Us"
                style={{
                  width: "100%",
                  height: "auto",
                  objectFit: "cover",
                  borderRadius: "8px",
                }}
              />
            </div>
          </div>
          <h2 style={styles.h2}>Event Details</h2>
          <p style={styles.p}>
            <strong>Event:</strong> {proposal.event.eventName}
          </p>
          <p style={styles.p}>
            <strong>Date:</strong> {proposal.event.eventDate}
          </p>
          <p style={styles.p}>
            <strong>Location:</strong> {proposal.event.eventLocation}
          </p>
          <p style={styles.p}>
            <strong>Audience:</strong> {proposal.event.audience}
          </p>
        </div>

        {proposal.speakers.map((speaker) => (
          <div
            key={speaker.speaker.id}
            className="pdf-page"
            style={styles.page}
          >
            <h2 style={styles.h2}>Proposed Speaker</h2>
            <div style={styles.speakerCard}>
              <img
                src={speaker.speaker.image}
                alt={speaker.speaker.name}
                style={styles.speakerImage}
              />
              <div>
                <h3 style={styles.h3}>{speaker.speaker.name}</h3>
                <p style={styles.p}>
                  <strong>Role:</strong> {speaker.role}
                </p>
                <p style={styles.p}>
                  <strong>Bio:</strong> {speaker.speaker.bio}
                </p>
              </div>
            </div>
            {speaker.notes && (
              <p style={styles.p}>
                <strong>Notes:</strong> {speaker.notes}
              </p>
            )}
          </div>
        ))}

        <div className="pdf-page" style={styles.page}>
          <h2 style={styles.h2}>Investment Summary</h2>
          <p style={styles.p}>
            Total Investment:{" "}
            <strong>${proposal.totalBudget.toLocaleString()}</strong>
          </p>
          <div style={styles.total}>
            Total: ${proposal.totalBudget.toLocaleString()}
          </div>
        </div>
      </body>
    </html>
  );
};
