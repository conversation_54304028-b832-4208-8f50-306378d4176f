import { Speaker } from "@/lib/services/speakers";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, User } from "lucide-react";

interface SpeakerCardProps {
  speaker: Speaker;
  onClick: () => void;
}

const SpeakerCard = ({ speaker, onClick }: SpeakerCardProps) => {
  const getAvailabilityColor = (availability: string | null) => {
    switch (availability) {
      case "Available":
        return "bg-green-100 text-green-800 border-green-200";
      case "Busy":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "Unavailable":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <Card
      className="cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 bg-card border-border flex flex-col"
      onClick={onClick}
    >
      <CardContent className="p-6 flex flex-col flex-grow">
        <div className="flex flex-col items-center text-center space-y-4 flex-grow">
          <div className="relative">
            <div className="w-24 h-24 rounded-full bg-muted flex items-center justify-center border-4 border-background shadow-md">
              <User className="w-12 h-12 text-muted-foreground" />
            </div>
            {speaker.availability && (
              <Badge
                variant="outline"
                className={`absolute -bottom-2 left-1/2 transform -translate-x-1/2 text-xs ${getAvailabilityColor(
                  speaker.availability
                )}`}
              >
                {speaker.availability}
              </Badge>
            )}
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-foreground">
              {speaker.name}
            </h3>
            {speaker.category && (
              <Badge variant="secondary" className="bg-primary/10 text-primary">
                {speaker.category}
              </Badge>
            )}
          </div>

          <p className="text-sm text-muted-foreground line-clamp-3 leading-relaxed flex-grow">
            {speaker.bio}
          </p>

          <div className="space-y-2 text-xs text-muted-foreground pt-2">
            <div className="flex items-center justify-center space-x-1">
              <MapPin size={12} />
              <span>{speaker.location || "International"}</span>
            </div>
          </div>

          <div className="pt-2 border-t border-border w-full mt-auto">
            {speaker.rate !== null && typeof speaker.rate !== "undefined" && (
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-foreground">
                  Rate:
                </span>
                <span className="text-lg font-bold text-primary">
                  ${speaker.rate.toLocaleString()}
                </span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SpeakerCard;
