
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface EventDetailsProps {
  eventDetails: {
    name: string;
    date: string;
    time: string;
    duration: string;
    location: string;
    description: string;
    audience_size: string;
    event_type: string;
    budget_range: string;
    special_requirements: string;
  };
  setEventDetails: (details: {
    name: string;
    date: string;
    time: string;
    duration: string;
    location: string;
    description: string;
    audience_size: string;
    event_type: string;
    budget_range: string;
    special_requirements: string;
  }) => void;
}

export const EventDetails = ({ eventDetails, setEventDetails }: EventDetailsProps) => {
  const handleChange = (field: keyof typeof eventDetails, value: string) => {
    setEventDetails({ ...eventDetails, [field]: value });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Event Details</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="eventName">Event Name *</Label>
          <Input
            id="eventName"
            value={eventDetails.name}
            onChange={(e) => handleChange("name", e.target.value)}
            placeholder="Enter event name"
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="eventDate">Event Date *</Label>
            <Input
              id="eventDate"
              type="date"
              value={eventDetails.date}
              onChange={(e) => handleChange("date", e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="eventTime">Event Time</Label>
            <Input
              id="eventTime"
              type="time"
              value={eventDetails.time}
              onChange={(e) => handleChange("time", e.target.value)}
            />
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="eventLocation">Location</Label>
          <Input
            id="eventLocation"
            value={eventDetails.location}
            onChange={(e) => handleChange("location", e.target.value)}
            placeholder="Enter event location"
          />
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="duration">Duration</Label>
            <Input
              id="duration"
              value={eventDetails.duration}
              onChange={(e) => handleChange("duration", e.target.value)}
              placeholder="2 hours"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="audienceSize">Audience Size</Label>
            <Input
              id="audienceSize"
              value={eventDetails.audience_size}
              onChange={(e) => handleChange("audience_size", e.target.value)}
              placeholder="500 professionals"
            />
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="eventType">Event Type</Label>
            <Select value={eventDetails.event_type} onValueChange={(value) => handleChange("event_type", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select event type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="conference">Conference</SelectItem>
                <SelectItem value="workshop">Workshop</SelectItem>
                <SelectItem value="webinar">Webinar</SelectItem>
                <SelectItem value="seminar">Seminar</SelectItem>
                <SelectItem value="corporate-event">Corporate Event</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="budgetRange">Budget Range</Label>
            <Select value={eventDetails.budget_range} onValueChange={(value) => handleChange("budget_range", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select budget range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="under-10k">Under $10,000</SelectItem>
                <SelectItem value="10k-25k">$10,000 - $25,000</SelectItem>
                <SelectItem value="25k-50k">$25,000 - $50,000</SelectItem>
                <SelectItem value="50k-100k">$50,000 - $100,000</SelectItem>
                <SelectItem value="over-100k">Over $100,000</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="space-y-2">
          <Label htmlFor="description">Event Description</Label>
          <Textarea
            id="description"
            value={eventDetails.description}
            onChange={(e) => handleChange("description", e.target.value)}
            placeholder="Describe the event, its purpose and goals"
            rows={3}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="specialRequirements">Special Requirements</Label>
          <Textarea
            id="specialRequirements"
            value={eventDetails.special_requirements}
            onChange={(e) => handleChange("special_requirements", e.target.value)}
            placeholder="Any specific requirements, constraints, or special needs"
            rows={3}
          />
        </div>
      </CardContent>
    </Card>
  );
};
