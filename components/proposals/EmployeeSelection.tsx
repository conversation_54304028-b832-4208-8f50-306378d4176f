import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Employee } from "@/types/employee";

interface EmployeeSelectionProps {
  selectedEmployeeId: string;
  setSelectedEmployeeId: (id: string) => void;
  employees?: Employee[];
}

export const EmployeeSelection = ({
  selectedEmployeeId,
  setSelectedEmployeeId,
  employees = [],
}: EmployeeSelectionProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Sales Representative *</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="employee">Select Sales Representative</Label>
          <Select
            value={selectedEmployeeId}
            onValueChange={setSelectedEmployeeId}
          >
            <SelectTrigger>
              <SelectValue placeholder="Choose a sales representative" />
            </SelectTrigger>
            <SelectContent>
              {employees.map((employee) => (
                <SelectItem key={employee.id} value={employee.id}>
                  {employee.name} - {employee.role}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  );
};
