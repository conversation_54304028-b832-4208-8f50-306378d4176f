import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Download,
  FileText,
  Send,
  Check,
  X,
  Save,
  ArrowLeft,
} from "lucide-react";

// Component for displaying proposal status and actions (for existing proposals)
interface ProposalStatusActionsProps {
  proposal: {
    id: string;
    status: string;
    event_name: string;
    created_at: string;
    details: any;
    pdf_path?: string;
  };
}

export const ProposalStatusActions = ({
  proposal,
}: ProposalStatusActionsProps) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "draft":
        return "bg-gray-100 text-gray-800";
      case "sent":
        return "bg-blue-100 text-blue-800";
      case "approved":
        return "bg-green-100 text-green-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <Badge className={getStatusColor(proposal.status || "draft")}>
        {proposal.status || "draft"}
      </Badge>

      {proposal.status === "draft" && (
        <Button size="sm" variant="outline">
          <Send className="h-4 w-4 mr-1" />
          Send
        </Button>
      )}

      <Button size="sm" variant="outline">
        <FileText className="h-4 w-4 mr-1" />
        Generate PDF
      </Button>

      {proposal.pdf_path && (
        <Button size="sm" variant="outline">
          <Download className="h-4 w-4 mr-1" />
          Download
        </Button>
      )}
    </div>
  );
};

// Component for save/cancel actions (for creating/editing proposals)
interface ProposalActionsProps {
  onSave: () => void;
  onCancel: () => void;
  isEditing: boolean;
  isLoading?: boolean;
}

export const ProposalActions = ({
  onSave,
  onCancel,
  isEditing,
  isLoading,
}: ProposalActionsProps) => {
  return (
    <div className="flex flex-col space-y-4 p-6 bg-muted/50 rounded-lg">
      <h3 className="text-lg font-semibold">Actions</h3>
      <div className="flex flex-col space-y-2">
        <Button onClick={onSave} disabled={isLoading} className="w-full">
          <Save className="h-4 w-4 mr-2" />
          {isLoading
            ? "Saving..."
            : isEditing
            ? "Update Proposal"
            : "Create Proposal"}
        </Button>
        <Button variant="outline" onClick={onCancel} className="w-full">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Cancel
        </Button>
      </div>
    </div>
  );
};
