import { pgTable, uuid, text, timestamp, integer, boolean, jsonb } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';

// Base tables
export const user = pgTable("user", {
    id: text("id").primaryKey(),
    name: text('name').notNull(),
    email: text('email').notNull().unique(),
    emailVerified: boolean('email_verified').$defaultFn(() => false).notNull(),
    image: text('image'),
    createdAt: timestamp('created_at').$defaultFn(() => /* @__PURE__ */ new Date()).notNull(),
    updatedAt: timestamp('updated_at').$defaultFn(() => /* @__PURE__ */ new Date()).notNull()
});

export const account = pgTable("account", {
    id: text('id').primaryKey(),
    accountId: text('account_id').notNull(),
    providerId: text('provider_id').notNull(),
    userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
    accessToken: text('access_token'),
    refreshToken: text('refresh_token'),
    idToken: text('id_token'),
    accessTokenExpiresAt: timestamp('access_token_expires_at'),
    refreshTokenExpiresAt: timestamp('refresh_token_expires_at'),
    scope: text('scope'),
    password: text('password'),
    createdAt: timestamp('created_at').notNull(),
    updatedAt: timestamp('updated_at').notNull()
});

export const session = pgTable("session", {
    id: text('id').primaryKey(),
    expiresAt: timestamp('expires_at').notNull(),
    token: text('token').notNull().unique(),
    createdAt: timestamp('created_at').notNull(),
    updatedAt: timestamp('updated_at').notNull(),
    ipAddress: text('ip_address'),
    userAgent: text('user_agent'),
    userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' })
});


export const verification = pgTable("verification", {
    id: text('id').primaryKey(),
    identifier: text('identifier').notNull(),
    value: text('value').notNull(),
    expiresAt: timestamp('expires_at').notNull(),
    createdAt: timestamp('created_at').$defaultFn(() => /* @__PURE__ */ new Date()),
    updatedAt: timestamp('updated_at').$defaultFn(() => /* @__PURE__ */ new Date())
});

// Application tables
export const employees = pgTable('employees', {
    id: uuid('id').primaryKey().defaultRandom(),
    name: text('name').notNull(),
    email: text('email').notNull(),
    role: text('role'),
    phone: text('phone'),
    imageUrl: text('image_url'),
    createdAt: timestamp('created_at').defaultNow(),
});

export const speakers = pgTable('speakers', {
    id: uuid('id').primaryKey().defaultRandom(),
    name: text('name').notNull(),
    bio: text('bio'),
    category: text('category'),
    rate: integer('rate'),
    location: text('location'),
    experience: text('experience'),
    createdAt: timestamp('created_at').defaultNow(),
    customRate: text('custom rate'),
});

export const proposals = pgTable('proposals', {
    id: uuid('id').primaryKey().defaultRandom(),
    eventName: text('event_name'),
    speakerId: uuid('speaker_id').references(() => speakers.id),
    details: jsonb('details'),
    status: text('status'),
    pdfPath: text('pdf_path'),
    submittedDate: timestamp('submitted_date'),
    createdAt: timestamp('created_at').defaultNow(),
});

export const proposalTemplates = pgTable('proposal_templates', {
    id: uuid('id').primaryKey().defaultRandom(),
    userId: uuid('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
    name: text('name').notNull(),
    coverPageTitle: text('cover_page_title').notNull(),
    coverPageSubtitle: text('cover_page_subtitle'),
    coverPageImageUrl: text('cover_page_image_url'),
    aboutUsMission: text('about_us_mission').notNull(),

    // Colors
    primaryColor: text('primary_color').notNull().default('#3B82F6'),
    secondaryColor: text('secondary_color').notNull().default('#1E40AF'),
    accentColor: text('accent_color').notNull().default('#F59E0B'),
    textColor: text('text_color').notNull().default('#1F2937'),
    backgroundColor: text('background_color').notNull().default('#FFFFFF'),

    // Typography
    headingFont: text('heading_font').notNull().default('Montserrat, sans-serif'),
    bodyFont: text('body_font').notNull().default('Open Sans, sans-serif'),
    fontSizeBase: integer('font_size_base').notNull().default(12),
    lineHeight: text('line_height').notNull().default('1.5'),

    // Layout
    pageMargin: integer('page_margin').notNull().default(20),
    sectionSpacing: integer('section_spacing').notNull().default(15),
    headerHeight: integer('header_height').notNull().default(80),
    footerHeight: integer('footer_height').notNull().default(50),

    // Page Structure
    includeCoverPage: boolean('include_cover_page').notNull().default(true),
    includeAboutPage: boolean('include_about_page').notNull().default(true),
    includeEventDetails: boolean('include_event_details').notNull().default(true),
    includeSpeakerProfiles: boolean('include_speaker_profiles').notNull().default(true),
    includeInvestmentSummary: boolean('include_investment_summary').notNull().default(true),
    includeThankYouPage: boolean('include_thank_you_page').notNull().default(true),

    // Content Options
    showSpeakerImages: boolean('show_speaker_images').notNull().default(true),
    showSpeakerBios: boolean('show_speaker_bios').notNull().default(true),
    showSpeakerRates: boolean('show_speaker_rates').notNull().default(true),
    showCompanyLogo: boolean('show_company_logo').notNull().default(true),
    watermarkText: text('watermark_text'),


    createdAt: timestamp('created_at').defaultNow(),
    updatedAt: timestamp('updated_at').defaultNow(),
});

export const categories = pgTable('categories', {
    id: uuid('id').primaryKey().defaultRandom(),
    name: text('name').notNull(),
    createdAt: timestamp('created_at').defaultNow(),
});

export const speakerImages = pgTable('speaker_images', {
    id: uuid('id').primaryKey().defaultRandom(),
    speakerId: uuid('speaker_id').notNull().references(() => speakers.id, { onDelete: 'cascade' }),
    imageUrl: text('image_url').notNull(),
    createdAt: timestamp('created_at').defaultNow(),
});

// Relations
export const userRelations = relations(user, ({ many }) => ({
    account: many(account),
    session: many(session),
    proposalTemplates: many(proposalTemplates),
}));

export const accountRelations = relations(account, ({ one }) => ({
    user: one(user, {
        fields: [account.userId],
        references: [user.id],
    }),
}));

export const sessionRelations = relations(session, ({ one }) => ({
    user: one(user, {
        fields: [session.userId],
        references: [user.id],
    }),
}));

export const speakersRelations = relations(speakers, ({ many }) => ({
    proposals: many(proposals),
    speakerImages: many(speakerImages),
}));

export const proposalsRelations = relations(proposals, ({ one }) => ({
    speaker: one(speakers, {
        fields: [proposals.speakerId],
        references: [speakers.id],
    }),
}));

export const proposalTemplatesRelations = relations(proposalTemplates, ({ one }) => ({
    user: one(user, {
        fields: [proposalTemplates.userId],
        references: [user.id],
    }),
}));

export const speakerImagesRelations = relations(speakerImages, ({ one }) => ({
    speaker: one(speakers, {
        fields: [speakerImages.speakerId],
        references: [speakers.id],
    }),
}));
