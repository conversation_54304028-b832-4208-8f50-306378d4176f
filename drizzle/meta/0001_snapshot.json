{"id": "95af9d43-09bf-4266-ad0d-3928a3aa16d8", "prevId": "1f080c53-bf39-4fa5-83b9-f77c89c01f9a", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.categories": {"name": "categories", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.employees": {"name": "employees", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.proposal_templates": {"name": "proposal_templates", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "cover_page_title": {"name": "cover_page_title", "type": "text", "primaryKey": false, "notNull": true}, "cover_page_subtitle": {"name": "cover_page_subtitle", "type": "text", "primaryKey": false, "notNull": false}, "cover_page_image_url": {"name": "cover_page_image_url", "type": "text", "primaryKey": false, "notNull": false}, "about_us_mission": {"name": "about_us_mission", "type": "text", "primaryKey": false, "notNull": true}, "primary_color": {"name": "primary_color", "type": "text", "primaryKey": false, "notNull": true, "default": "'#3B82F6'"}, "secondary_color": {"name": "secondary_color", "type": "text", "primaryKey": false, "notNull": true, "default": "'#1E40AF'"}, "accent_color": {"name": "accent_color", "type": "text", "primaryKey": false, "notNull": true, "default": "'#F59E0B'"}, "text_color": {"name": "text_color", "type": "text", "primaryKey": false, "notNull": true, "default": "'#1F2937'"}, "background_color": {"name": "background_color", "type": "text", "primaryKey": false, "notNull": true, "default": "'#FFFFFF'"}, "heading_font": {"name": "heading_font", "type": "text", "primaryKey": false, "notNull": true, "default": "'Montserrat, sans-serif'"}, "body_font": {"name": "body_font", "type": "text", "primaryKey": false, "notNull": true, "default": "'Open Sans, sans-serif'"}, "font_size_base": {"name": "font_size_base", "type": "integer", "primaryKey": false, "notNull": true, "default": 12}, "line_height": {"name": "line_height", "type": "text", "primaryKey": false, "notNull": true, "default": "'1.5'"}, "page_margin": {"name": "page_margin", "type": "integer", "primaryKey": false, "notNull": true, "default": 20}, "section_spacing": {"name": "section_spacing", "type": "integer", "primaryKey": false, "notNull": true, "default": 15}, "header_height": {"name": "header_height", "type": "integer", "primaryKey": false, "notNull": true, "default": 80}, "footer_height": {"name": "footer_height", "type": "integer", "primaryKey": false, "notNull": true, "default": 50}, "include_cover_page": {"name": "include_cover_page", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "include_about_page": {"name": "include_about_page", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "include_event_details": {"name": "include_event_details", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "include_speaker_profiles": {"name": "include_speaker_profiles", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "include_investment_summary": {"name": "include_investment_summary", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "include_thank_you_page": {"name": "include_thank_you_page", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "show_speaker_images": {"name": "show_speaker_images", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "show_speaker_bios": {"name": "show_speaker_bios", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "show_speaker_rates": {"name": "show_speaker_rates", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "show_company_logo": {"name": "show_company_logo", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "watermark_text": {"name": "watermark_text", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"proposal_templates_user_id_user_id_fk": {"name": "proposal_templates_user_id_user_id_fk", "tableFrom": "proposal_templates", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.proposals": {"name": "proposals", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "event_name": {"name": "event_name", "type": "text", "primaryKey": false, "notNull": false}, "speaker_id": {"name": "speaker_id", "type": "uuid", "primaryKey": false, "notNull": false}, "details": {"name": "details", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "pdf_path": {"name": "pdf_path", "type": "text", "primaryKey": false, "notNull": false}, "submitted_date": {"name": "submitted_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"proposals_speaker_id_speakers_id_fk": {"name": "proposals_speaker_id_speakers_id_fk", "tableFrom": "proposals", "tableTo": "speakers", "columnsFrom": ["speaker_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.speaker_images": {"name": "speaker_images", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "speaker_id": {"name": "speaker_id", "type": "uuid", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"speaker_images_speaker_id_speakers_id_fk": {"name": "speaker_images_speaker_id_speakers_id_fk", "tableFrom": "speaker_images", "tableTo": "speakers", "columnsFrom": ["speaker_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.speakers": {"name": "speakers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "rate": {"name": "rate", "type": "integer", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "experience": {"name": "experience", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "custom rate": {"name": "custom rate", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}