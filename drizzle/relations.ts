import { relations } from "drizzle-orm/relations";
import { speakers, proposals, speakerImages } from "./schema";

export const proposalsRelations = relations(proposals, ({one}) => ({
	speaker: one(speakers, {
		fields: [proposals.speakerId],
		references: [speakers.id]
	}),
}));

export const speakersRelations = relations(speakers, ({many}) => ({
	proposals: many(proposals),
	speakerImages: many(speakerImages),
}));

export const speakerImagesRelations = relations(speakerImages, ({one}) => ({
	speaker: one(speakers, {
		fields: [speakerImages.speakerId],
		references: [speakers.id]
	}),
}));