import { pgTable, uuid, text, integer, unique, timestamp, foreignKey, jsonb, boolean, primaryKey } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"



export const accounts = pgTable("accounts", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: uuid().notNull(),
	type: text().notNull(),
	provider: text().notNull(),
	providerAccountId: text().notNull(),
	refreshToken: text("refresh_token"),
	accessToken: text("access_token"),
	expiresAt: integer("expires_at"),
	tokenType: text("token_type"),
	scope: text(),
	idToken: text("id_token"),
	sessionState: text("session_state"),
});

export const sessions = pgTable("sessions", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	sessionToken: text().notNull(),
	userId: uuid().notNull(),
	expires: timestamp({ mode: 'string' }).notNull(),
}, (table) => [
	unique("sessions_sessionToken_unique").on(table.sessionToken),
]);

export const employees = pgTable("employees", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: text().notNull(),
	email: text().notNull(),
	role: text(),
	phone: text(),
	imageUrl: text("image_url"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
});

export const proposals = pgTable("proposals", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	eventName: text("event_name"),
	speakerId: uuid("speaker_id"),
	details: jsonb(),
	status: text(),
	pdfPath: text("pdf_path"),
	submittedDate: timestamp("submitted_date", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.speakerId],
			foreignColumns: [speakers.id],
			name: "proposals_speaker_id_speakers_id_fk"
		}),
]);

export const speakers = pgTable("speakers", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: text().notNull(),
	bio: text(),
	category: text(),
	rate: integer(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	location: text(),
	experience: text(),
	customRate: text("custom rate"),
});

export const categories = pgTable("categories", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: text().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
});

export const speakerImages = pgTable("speaker_images", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	speakerId: uuid("speaker_id").notNull(),
	imageUrl: text("image_url").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.speakerId],
			foreignColumns: [speakers.id],
			name: "speaker_images_speaker_id_speakers_id_fk"
		}).onDelete("cascade"),
]);

export const proposalTemplates = pgTable("proposal_templates", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	userId: uuid("user_id").notNull(),
	name: text().notNull(),
	coverPageTitle: text("cover_page_title").notNull(),
	coverPageSubtitle: text("cover_page_subtitle"),
	coverPageImageUrl: text("cover_page_image_url"),
	aboutUsMission: text("about_us_mission").notNull(),
	primaryColor: text("primary_color").default('#3B82F6').notNull(),
	secondaryColor: text("secondary_color").default('#1E40AF').notNull(),
	accentColor: text("accent_color").default('#F59E0B').notNull(),
	textColor: text("text_color").default('#1F2937').notNull(),
	backgroundColor: text("background_color").default('#FFFFFF').notNull(),
	headingFont: text("heading_font").default('Montserrat, sans-serif').notNull(),
	bodyFont: text("body_font").default('Open Sans, sans-serif').notNull(),
	fontSizeBase: integer("font_size_base").default(12).notNull(),
	lineHeight: text("line_height").default('1.5').notNull(),
	pageMargin: integer("page_margin").default(20).notNull(),
	sectionSpacing: integer("section_spacing").default(15).notNull(),
	headerHeight: integer("header_height").default(80).notNull(),
	footerHeight: integer("footer_height").default(50).notNull(),
	includeCoverPage: boolean("include_cover_page").default(true).notNull(),
	includeAboutPage: boolean("include_about_page").default(true).notNull(),
	includeEventDetails: boolean("include_event_details").default(true).notNull(),
	includeSpeakerProfiles: boolean("include_speaker_profiles").default(true).notNull(),
	includeInvestmentSummary: boolean("include_investment_summary").default(true).notNull(),
	includeThankYouPage: boolean("include_thank_you_page").default(true).notNull(),
	showSpeakerImages: boolean("show_speaker_images").default(true).notNull(),
	showSpeakerBios: boolean("show_speaker_bios").default(true).notNull(),
	showSpeakerRates: boolean("show_speaker_rates").default(true).notNull(),
	showCompanyLogo: boolean("show_company_logo").default(true).notNull(),
	watermarkText: text("watermark_text"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
});

export const user = pgTable("user", {
	id: text().primaryKey().notNull(),
	name: text().notNull(),
	email: text().notNull(),
	emailVerified: boolean("email_verified").default(false).notNull(),
	image: text(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	unique("user_email_unique").on(table.email),
]);

export const verificationToken = pgTable("verificationToken", {
	identifier: text().notNull(),
	token: text().notNull(),
	expires: timestamp({ mode: 'string' }).notNull(),
}, (table) => [
	primaryKey({ columns: [table.identifier, table.token], name: "verificationToken_identifier_token_pk"}),
]);
