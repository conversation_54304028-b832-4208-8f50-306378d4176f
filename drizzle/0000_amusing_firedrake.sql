CREATE TABLE "account" (
	"id" text PRIMARY KEY NOT NULL,
	"account_id" text NOT NULL,
	"provider_id" text NOT NULL,
	"user_id" text NOT NULL,
	"access_token" text,
	"refresh_token" text,
	"id_token" text,
	"access_token_expires_at" timestamp,
	"refresh_token_expires_at" timestamp,
	"scope" text,
	"password" text,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "categories" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "employees" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"role" text,
	"phone" text,
	"image_url" text,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "proposal_templates" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"name" text NOT NULL,
	"cover_page_title" text NOT NULL,
	"cover_page_subtitle" text,
	"cover_page_image_url" text,
	"about_us_mission" text NOT NULL,
	"primary_color" text DEFAULT '#3B82F6' NOT NULL,
	"secondary_color" text DEFAULT '#1E40AF' NOT NULL,
	"accent_color" text DEFAULT '#F59E0B' NOT NULL,
	"text_color" text DEFAULT '#1F2937' NOT NULL,
	"background_color" text DEFAULT '#FFFFFF' NOT NULL,
	"heading_font" text DEFAULT 'Montserrat, sans-serif' NOT NULL,
	"body_font" text DEFAULT 'Open Sans, sans-serif' NOT NULL,
	"font_size_base" integer DEFAULT 12 NOT NULL,
	"line_height" text DEFAULT '1.5' NOT NULL,
	"page_margin" integer DEFAULT 20 NOT NULL,
	"section_spacing" integer DEFAULT 15 NOT NULL,
	"header_height" integer DEFAULT 80 NOT NULL,
	"footer_height" integer DEFAULT 50 NOT NULL,
	"include_cover_page" boolean DEFAULT true NOT NULL,
	"include_about_page" boolean DEFAULT true NOT NULL,
	"include_event_details" boolean DEFAULT true NOT NULL,
	"include_speaker_profiles" boolean DEFAULT true NOT NULL,
	"include_investment_summary" boolean DEFAULT true NOT NULL,
	"include_thank_you_page" boolean DEFAULT true NOT NULL,
	"show_speaker_images" boolean DEFAULT true NOT NULL,
	"show_speaker_bios" boolean DEFAULT true NOT NULL,
	"show_speaker_rates" boolean DEFAULT true NOT NULL,
	"show_company_logo" boolean DEFAULT true NOT NULL,
	"watermark_text" text,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "proposals" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"event_name" text,
	"speaker_id" uuid,
	"details" jsonb,
	"status" text,
	"pdf_path" text,
	"submitted_date" timestamp,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "session" (
	"id" text PRIMARY KEY NOT NULL,
	"expires_at" timestamp NOT NULL,
	"token" text NOT NULL,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL,
	"ip_address" text,
	"user_agent" text,
	"user_id" text NOT NULL,
	CONSTRAINT "session_token_unique" UNIQUE("token")
);
--> statement-breakpoint
CREATE TABLE "speaker_images" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"speaker_id" uuid NOT NULL,
	"image_url" text NOT NULL,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "speakers" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"bio" text,
	"category" text,
	"rate" integer,
	"location" text,
	"experience" text,
	"created_at" timestamp DEFAULT now(),
	"custom rate" text
);
--> statement-breakpoint
CREATE TABLE "user" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"email_verified" boolean NOT NULL,
	"image" text,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL,
	CONSTRAINT "user_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "verification" (
	"id" text PRIMARY KEY NOT NULL,
	"identifier" text NOT NULL,
	"value" text NOT NULL,
	"expires_at" timestamp NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
ALTER TABLE "account" ADD CONSTRAINT "account_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "proposal_templates" ADD CONSTRAINT "proposal_templates_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "proposals" ADD CONSTRAINT "proposals_speaker_id_speakers_id_fk" FOREIGN KEY ("speaker_id") REFERENCES "public"."speakers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "session" ADD CONSTRAINT "session_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "speaker_images" ADD CONSTRAINT "speaker_images_speaker_id_speakers_id_fk" FOREIGN KEY ("speaker_id") REFERENCES "public"."speakers"("id") ON DELETE cascade ON UPDATE no action;