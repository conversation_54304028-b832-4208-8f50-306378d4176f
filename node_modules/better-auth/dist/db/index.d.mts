import { a as Adapter, B as BetterAuthOptions, G as GenericEndpointContext, Z as TransactionAdapter, Y as Where, M as FieldAttribute, E as FieldType, K as KyselyDatabaseType } from '../shared/better-auth.-1DAdYNj.mjs';
export { al as BetterAuthDbSchema, J as FieldAttributeConfig, ai as FieldAttributeToObject, aj as InferAdditionalFieldsFromPluginOptions, l as InferFieldsFromOptions, k as InferFieldsFromPlugins, ag as InferFieldsInput, ah as InferFieldsInputClient, af as InferFieldsOutput, ae as InferValueType, ac as InternalAdapter, ak as PluginFieldAttribute, ao as accountSchema, an as coreSchema, ad as createFieldAttribute, ab as createInternalAdapter, at as getAllFields, am as getAuthTables, aC as mergeSchema, aA as parseAccountInput, av as parseAccountOutput, az as parseAdditionalUserInput, ax as parseInputData, as as parseOutputData, aB as parseSessionInput, aw as parseSessionOutput, ay as parseUserInput, au as parseUserOutput, aq as sessionSchema, ap as userSchema, ar as verificationSchema } from '../shared/better-auth.-1DAdYNj.mjs';
import * as z from 'zod';
import 'kysely';
import 'better-call';
import '../shared/better-auth.DTtXpZYr.mjs';
import '../shared/better-auth.BHPr8J54.mjs';
import 'zod/v4/core';
import 'better-sqlite3';
import 'bun:sqlite';
import 'node:sqlite';

declare function getWithHooks(adapter: Adapter, ctx: {
    options: BetterAuthOptions;
    hooks: Exclude<BetterAuthOptions["databaseHooks"], undefined>[];
}): {
    createWithHooks: <T extends Record<string, any>>(data: T, model: "user" | "session" | "account" | "verification", customCreateFn?: {
        fn: (data: Record<string, any>) => void | Promise<any>;
        executeMainFn?: boolean;
    }, context?: GenericEndpointContext, trxAdapter?: TransactionAdapter) => Promise<any>;
    updateWithHooks: <T extends Record<string, any>>(data: any, where: Where[], model: "user" | "session" | "account" | "verification", customUpdateFn?: {
        fn: (data: Record<string, any>) => void | Promise<any>;
        executeMainFn?: boolean;
    }, context?: GenericEndpointContext, trxAdapter?: TransactionAdapter) => Promise<any>;
    updateManyWithHooks: <T extends Record<string, any>>(data: any, where: Where[], model: "user" | "session" | "account" | "verification", customUpdateFn?: {
        fn: (data: Record<string, any>) => void | Promise<any>;
        executeMainFn?: boolean;
    }, context?: GenericEndpointContext, trxAdapter?: TransactionAdapter) => Promise<any>;
};

declare function toZodSchema<Fields extends Record<string, FieldAttribute | never>, IsClientSide extends boolean>({ fields, isClientSide, }: {
    fields: Fields;
    /**
     * If true, then any fields that have `input: false` will be removed from the schema to prevent user input.
     */
    isClientSide: IsClientSide;
}): z.ZodObject<RemoveNeverProps<{ [key in keyof Fields]: FieldAttributeToSchema<Fields[key], IsClientSide>; }>, z.core.$strip>;
type FieldAttributeToSchema<Field extends FieldAttribute | Record<string, never>, isClientSide extends boolean = false> = Field extends {
    type: any;
} ? GetInput<isClientSide, Field, GetRequired<Field, GetType<Field>>> : Record<string, never>;
type GetType<F extends FieldAttribute> = F extends {
    type: "string";
} ? z.ZodString : F extends {
    type: "number";
} ? z.ZodNumber : F extends {
    type: "boolean";
} ? z.ZodBoolean : F extends {
    type: "date";
} ? z.ZodDate : z.ZodAny;
type GetRequired<F extends FieldAttribute, Schema extends z.core.SomeType> = F extends {
    required: true;
} ? Schema : z.ZodOptional<Schema>;
type GetInput<isClientSide extends boolean, Field extends FieldAttribute, Schema extends z.core.SomeType> = Field extends {
    input: false;
} ? isClientSide extends true ? never : Schema : Schema;
type RemoveNeverProps<T> = {
    [K in keyof T as [T[K]] extends [never] ? never : K]: T[K];
};

declare function getAdapter(options: BetterAuthOptions): Promise<Adapter>;
declare function convertToDB<T extends Record<string, any>>(fields: Record<string, FieldAttribute>, values: T): T;
declare function convertFromDB<T extends Record<string, any>>(fields: Record<string, FieldAttribute>, values: T | null): T | null;

declare function matchType(columnDataType: string, fieldType: FieldType, dbType: KyselyDatabaseType): boolean;
declare function getMigrations(config: BetterAuthOptions): Promise<{
    toBeCreated: {
        table: string;
        fields: Record<string, FieldAttribute>;
        order: number;
    }[];
    toBeAdded: {
        table: string;
        fields: Record<string, FieldAttribute>;
        order: number;
    }[];
    runMigrations: () => Promise<void>;
    compileMigrations: () => Promise<string>;
}>;

declare function getSchema(config: BetterAuthOptions): Record<string, {
    fields: Record<string, FieldAttribute>;
    order: number;
}>;

export { FieldAttribute, FieldType, convertFromDB, convertToDB, getAdapter, getMigrations, getSchema, getWithHooks, matchType, toZodSchema };
export type { FieldAttributeToSchema };
