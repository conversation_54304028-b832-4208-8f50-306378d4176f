export { Q as Account, a as Adapter, $ as AdapterInstance, _ as AdapterSchemaCreation, P as AdditionalSessionFieldsInput, i as AdditionalSessionFieldsOutput, m as AdditionalUserFieldsInput, j as AdditionalUserFieldsOutput, d as AuthContext, v as AuthPluginSchema, B as BetterAuthOptions, n as BetterAuthPlugin, F as FilterActions, a1 as FilteredAPI, G as GenericEndpointContext, H as HookEndpointContext, h as InferAPI, I as InferOptionSchema, p as InferPluginErrorCodes, o as InferPluginTypes, f as InferSession, a2 as InferSessionAPI, e as InferUser, N as Models, R as RateLimit, a0 as SecondaryStorage, S as Session, Z as TransactionAdapter, U as User, V as Verification, Y as Where, X as init } from '../shared/better-auth.C8uxRGim.js';
export { A as AtomListener, B as BetterAuthClientPlugin, C as ClientOptions, b as InferActions, i as InferAdditionalFromClient, a as InferClientAPI, c as InferErrorCodes, f as InferPluginsFromClient, g as InferSessionFromClient, h as InferUserFromClient, I as IsSignal, S as SessionQueryParams, e as Store } from '../shared/better-auth.Dvz5kVNu.js';
import 'zod';
import 'kysely';
import 'better-call';
import '../shared/better-auth.DTtXpZYr.js';
import '../shared/better-auth.C6qXK08w.js';
import 'zod/v4/core';
import 'better-sqlite3';
import 'bun:sqlite';
import 'node:sqlite';
import '@better-fetch/fetch';
import 'nanostores';
