import '../shared/better-auth.C6qXK08w.js';
export { z as AuthEndpoint, D as AuthMiddleware, br as accountInfo, aT as callbackOAuth, bi as changeEmail, be as changePassword, aO as checkEndpointConflicts, y as createAuthEndpoint, x as createAuthMiddleware, b9 as createEmailVerificationToken, bg as deleteUser, bh as deleteUserCallback, bj as error, b5 as forgetPassword, b7 as forgetPasswordCallback, a_ as freshSessionMiddleware, bp as getAccessToken, aP as getEndpoints, aV as getSession, aW as getSessionFromCtx, aU as getSessionQuerySchema, bn as linkSocialAccount, a$ as listSessions, bm as listUserAccounts, bk as ok, w as optionsMiddleware, bt as originCheck, bs as originCheckMiddleware, bq as refreshToken, aZ as requestOnlySessionMiddleware, b4 as requestPasswordReset, b6 as requestPasswordResetCallback, b8 as resetPassword, b2 as revokeOtherSessions, b0 as revokeSession, b1 as revokeSessions, aQ as router, bb as sendVerificationEmail, ba as sendVerificationEmailFn, aY as sensitiveSessionMiddleware, aX as sessionMiddleware, bf as setPassword, aS as signInEmail, aR as signInSocial, b3 as signOut, bl as signUpEmail, bo as unlinkAccount, bd as updateUser, bc as verifyEmail } from '../shared/better-auth.C8uxRGim.js';
export { APIError } from 'better-call';
import 'zod';
import '../shared/better-auth.DTtXpZYr.js';
import 'zod/v4/core';
import 'kysely';
import 'better-sqlite3';
import 'bun:sqlite';
import 'node:sqlite';
