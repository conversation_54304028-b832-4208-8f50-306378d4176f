import { B as BetterAuthOptions, T as TelemetryContext, L as TelemetryEvent, M as FieldAttribute, N as Models } from './shared/better-auth.-1DAdYNj.mjs';
export { Q as Account, a as Adapter, $ as AdapterInstance, _ as AdapterSchemaCreation, P as AdditionalSessionFieldsInput, i as AdditionalSessionFieldsOutput, m as AdditionalUserFieldsInput, j as AdditionalUserFieldsOutput, c as Auth, d as AuthContext, v as AuthPluginSchema, n as BetterAuthPlugin, F as FilterActions, a1 as FilteredAPI, G as GenericEndpointContext, H as HookEndpointContext, h as InferAPI, I as InferOptionSchema, p as InferPluginErrorCodes, o as InferPluginTypes, f as InferSession, a2 as InferSessionAPI, e as InferUser, a8 as InternalLogger, a7 as LogHandlerParams, a3 as LogLevel, a6 as Logger, R as RateLimit, a0 as SecondaryStorage, S as Session, Z as TransactionAdapter, U as User, V as Verification, Y as Where, W as WithJsDoc, O as betterAuth, a9 as createLogger, X as init, a4 as levels, aa as logger, a5 as shouldPublishLog } from './shared/better-auth.-1DAdYNj.mjs';
export { A as AtomListener, B as BetterAuthClientPlugin, C as ClientOptions, b as InferActions, i as InferAdditionalFromClient, a as InferClientAPI, c as InferErrorCodes, f as InferPluginsFromClient, g as InferSessionFromClient, h as InferUserFromClient, I as IsSignal, S as SessionQueryParams, e as Store } from './shared/better-auth.BmsVRmKx.mjs';
export { H as HIDE_METADATA } from './shared/better-auth.DEHJp1rk.mjs';
export { g as generateState, p as parseState } from './shared/better-auth.C5rlSioT.mjs';
export * from 'better-call';
export { APIError } from 'better-call';
export * from 'zod/v4';
export * from 'zod/v4/core';
import { b as LiteralUnion } from './shared/better-auth.DTtXpZYr.mjs';
export { A as Awaitable, D as DeepPartial, E as Expand, H as HasRequiredKeys, d as LiteralNumber, L as LiteralString, O as OmitId, e as PreserveJSDoc, a as Prettify, P as PrettifyDeep, c as Primitive, R as RequiredKeysOf, S as StripEmptyObjects, U as UnionToIntersection, W as WithoutEmpty } from './shared/better-auth.DTtXpZYr.mjs';
export { O as OAuth2Tokens, a as OAuth2UserInfo, b as OAuthProvider, P as ProviderOptions } from './shared/better-auth.BHPr8J54.mjs';
import 'zod';
import 'kysely';
import 'better-sqlite3';
import 'bun:sqlite';
import 'node:sqlite';
import '@better-fetch/fetch';
import 'nanostores';

declare function capitalizeFirstLetter(str: string): string;

declare const generateId: (size?: number) => string;

declare class BetterAuthError extends Error {
    constructor(message: string, cause?: string);
}
declare class MissingDependencyError extends BetterAuthError {
    constructor(pkgName: string);
}

declare function createTelemetry(options: BetterAuthOptions, context?: TelemetryContext): Promise<{
    publish: (event: TelemetryEvent) => Promise<void>;
}>;

declare function getTelemetryAuthConfig(options: BetterAuthOptions, context?: TelemetryContext): {
    database: string | undefined;
    adapter: string | undefined;
    emailVerification: {
        sendVerificationEmail: boolean;
        sendOnSignUp: boolean;
        sendOnSignIn: boolean;
        autoSignInAfterVerification: boolean;
        expiresIn: number | undefined;
        onEmailVerification: boolean;
        afterEmailVerification: boolean;
    };
    emailAndPassword: {
        enabled: boolean;
        disableSignUp: boolean;
        requireEmailVerification: boolean;
        maxPasswordLength: number | undefined;
        minPasswordLength: number | undefined;
        sendResetPassword: boolean;
        resetPasswordTokenExpiresIn: number | undefined;
        onPasswordReset: boolean;
        password: {
            hash: boolean;
            verify: boolean;
        };
        autoSignIn: boolean;
        revokeSessionsOnPasswordReset: boolean;
    };
    socialProviders: ({
        id?: undefined;
        mapProfileToUser?: undefined;
        disableDefaultScope?: undefined;
        disableIdTokenSignIn?: undefined;
        disableImplicitSignUp?: undefined;
        disableSignUp?: undefined;
        getUserInfo?: undefined;
        overrideUserInfoOnSignIn?: undefined;
        prompt?: undefined;
        verifyIdToken?: undefined;
        scope?: undefined;
        refreshAccessToken?: undefined;
    } | {
        id: string;
        mapProfileToUser: boolean;
        disableDefaultScope: boolean;
        disableIdTokenSignIn: boolean;
        disableImplicitSignUp: boolean | undefined;
        disableSignUp: boolean | undefined;
        getUserInfo: boolean;
        overrideUserInfoOnSignIn: boolean;
        prompt: "select_account" | "consent" | "login" | "none" | "select_account consent" | undefined;
        verifyIdToken: boolean;
        scope: string[] | undefined;
        refreshAccessToken: boolean;
    })[];
    plugins: string[] | undefined;
    user: {
        modelName: string | undefined;
        fields: Partial<Record<"name" | "emailVerified" | "email" | "image" | "createdAt" | "updatedAt", string>> | undefined;
        additionalFields: {
            [key: string]: FieldAttribute;
        } | undefined;
        changeEmail: {
            enabled: boolean | undefined;
            sendChangeEmailVerification: boolean;
        };
    };
    verification: {
        modelName: string | undefined;
        disableCleanup: boolean | undefined;
        fields: Partial<Record<"createdAt" | "updatedAt" | "expiresAt" | "value" | "identifier", string>> | undefined;
    };
    session: {
        modelName: string | undefined;
        additionalFields: {
            [key: string]: FieldAttribute;
        } | undefined;
        cookieCache: {
            enabled: boolean | undefined;
            maxAge: number | undefined;
        };
        disableSessionRefresh: boolean | undefined;
        expiresIn: number | undefined;
        fields: Partial<Record<"token" | "createdAt" | "updatedAt" | "userId" | "expiresAt" | "ipAddress" | "userAgent", string>> | undefined;
        freshAge: number | undefined;
        preserveSessionInDatabase: boolean | undefined;
        storeSessionInDatabase: boolean | undefined;
        updateAge: number | undefined;
    };
    account: {
        modelName: string | undefined;
        fields: Partial<Record<"scope" | "accessToken" | "refreshToken" | "accessTokenExpiresAt" | "refreshTokenExpiresAt" | "idToken" | "createdAt" | "updatedAt" | "userId" | "providerId" | "accountId" | "password", string>> | undefined;
        encryptOAuthTokens: boolean | undefined;
        updateAccountOnSignIn: boolean | undefined;
        accountLinking: {
            enabled: boolean | undefined;
            trustedProviders: LiteralUnion<"apple" | "atlassian" | "cognito" | "discord" | "facebook" | "figma" | "github" | "microsoft" | "google" | "huggingface" | "slack" | "spotify" | "twitch" | "twitter" | "dropbox" | "kick" | "linear" | "linkedin" | "gitlab" | "tiktok" | "reddit" | "roblox" | "salesforce" | "vk" | "zoom" | "notion" | "kakao" | "naver" | "line" | "paypal" | "email-password", string>[] | undefined;
            updateUserInfoOnLink: boolean | undefined;
            allowUnlinkingAll: boolean | undefined;
        };
    };
    hooks: {
        after: boolean;
        before: boolean;
    };
    secondaryStorage: boolean;
    advanced: {
        cookiePrefix: boolean;
        cookies: boolean;
        crossSubDomainCookies: {
            domain: boolean;
            enabled: boolean | undefined;
            additionalCookies: string[] | undefined;
        };
        database: {
            useNumberId: boolean;
            generateId: false | ((options: {
                model: LiteralUnion<Models, string>;
                size?: number;
            }) => string | false) | undefined;
            defaultFindManyLimit: number | undefined;
        };
        useSecureCookies: boolean | undefined;
        ipAddress: {
            disableIpTracking: boolean | undefined;
            ipAddressHeaders: string[] | undefined;
        };
        disableCSRFCheck: boolean | undefined;
        cookieAttributes: {
            expires: Date | undefined;
            secure: boolean | undefined;
            sameSite: "none" | "lax" | "Strict" | "Lax" | "None" | "strict" | undefined;
            domain: boolean;
            path: string | undefined;
            httpOnly: boolean | undefined;
        };
    };
    trustedOrigins: number | undefined;
    rateLimit: {
        storage: "database" | "memory" | "secondary-storage" | undefined;
        modelName: string | undefined;
        window: number | undefined;
        customStorage: boolean;
        enabled: boolean | undefined;
        max: number | undefined;
    };
    onAPIError: {
        errorURL: string | undefined;
        onError: boolean;
        throw: boolean | undefined;
    };
    logger: {
        disabled: boolean | undefined;
        level: "error" | "info" | "warn" | "debug" | undefined;
        log: boolean;
    };
    databaseHooks: {
        user: {
            create: {
                after: boolean;
                before: boolean;
            };
            update: {
                after: boolean;
                before: boolean;
            };
        };
        session: {
            create: {
                after: boolean;
                before: boolean;
            };
            update: {
                after: boolean;
                before: boolean;
            };
        };
        account: {
            create: {
                after: boolean;
                before: boolean;
            };
            update: {
                after: boolean;
                before: boolean;
            };
        };
        verification: {
            create: {
                after: boolean;
                before: boolean;
            };
            update: {
                after: boolean;
                before: boolean;
            };
        };
    };
};

export { BetterAuthError, BetterAuthOptions, LiteralUnion, MissingDependencyError, Models, TelemetryEvent, capitalizeFirstLetter, createTelemetry, generateId, getTelemetryAuthConfig };
