import { B as BetterAuthOptions, a as Adapter, q as AdapterFactoryOptions, r as AdapterFactoryConfig, t as AdapterFactoryCustomizeAdapterCreator } from '../shared/better-auth.C8uxRGim.js';
export { A as AdapterDebugLogs, u as AdapterTestDebugLogs, C as CustomAdapter } from '../shared/better-auth.C8uxRGim.js';
import 'zod';
import 'kysely';
import 'better-call';
import '../shared/better-auth.DTtXpZYr.js';
import '../shared/better-auth.C6qXK08w.js';
import 'zod/v4/core';
import 'better-sqlite3';
import 'bun:sqlite';
import 'node:sqlite';

type AdapterFactory = (options: BetterAuthOptions) => Adapter;
declare const createAdapterFactory: ({ adapter: customAdapter, config: cfg, }: AdapterFactoryOptions) => AdapterFactory;

/**
 * @deprecated Use `createAdapterFactory` instead. This export will be removed in the next major version.
 */
declare const createAdapter: ({ adapter: customAdapter, config: cfg, }: AdapterFactoryOptions) => AdapterFactory;
/**
 * @deprecated Use `AdapterFactoryOptions` instead. This export will be removed in the next major version.
 */
type CreateAdapterOptions = AdapterFactoryOptions;
/**
 * @deprecated Use `AdapterFactoryConfig` instead. This export will be removed in the next major version.
 */
type AdapterConfig = AdapterFactoryConfig;
/**
 * @deprecated Use `AdapterFactoryCustomizeAdapterCreator` instead. This export will be removed in the next major version.
 */
type CreateCustomAdapter = AdapterFactoryCustomizeAdapterCreator;

export { AdapterFactoryConfig, AdapterFactoryCustomizeAdapterCreator, AdapterFactoryOptions, createAdapter, createAdapterFactory };
export type { AdapterConfig, AdapterFactory, CreateAdapterOptions, CreateCustomAdapter };
