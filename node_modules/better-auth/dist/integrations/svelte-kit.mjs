import 'better-call';
import '../shared/better-auth.BPua9lvR.mjs';
import { c as createAuthMiddleware } from '../shared/better-auth.CBY7cUGy.mjs';
import 'zod';
import '../shared/better-auth.CiuwFiHM.mjs';
import '@better-auth/utils/base64';
import '@better-auth/utils/hmac';
import '../shared/better-auth.C4g12FAA.mjs';
import '@better-auth/utils/binary';
import { p as parseSetCookieHeader } from '../shared/better-auth.fV_ra52g.mjs';
import '../shared/better-auth.Dlx1jCB2.mjs';
import '../shared/better-auth.CW6D9eSx.mjs';
import '@better-auth/utils/hash';
import '../crypto/index.mjs';
import '@noble/ciphers/chacha.js';
import '@noble/ciphers/utils.js';
import 'jose';
import '@noble/hashes/scrypt.js';
import '@better-auth/utils/hex';
import '@noble/hashes/utils.js';
import '../shared/better-auth.DdzSJf-n.mjs';
import '../shared/better-auth.B4Qoxdgc.mjs';
import '@better-auth/utils/random';
import '@better-fetch/fetch';
import '../shared/better-auth.DxV4YGX3.mjs';
import 'jose/errors';
import '../shared/better-auth.CvNZNAOW.mjs';
import '../shared/better-auth.BUPPRXfK.mjs';
import 'defu';

const toSvelteKitHandler = (auth) => {
  return (event) => auth.handler(event.request);
};
const svelteKitHandler = async ({
  auth,
  event,
  resolve,
  building
}) => {
  if (building) {
    return resolve(event);
  }
  const { request, url } = event;
  if (isAuthPath(url.toString(), auth.options)) {
    return auth.handler(request);
  }
  return resolve(event);
};
function isAuthPath(url, options) {
  const _url = new URL(url);
  const baseURL = new URL(
    `${options.baseURL || _url.origin}${options.basePath || "/api/auth"}`
  );
  if (_url.origin !== baseURL.origin) return false;
  if (!_url.pathname.startsWith(
    baseURL.pathname.endsWith("/") ? baseURL.pathname : `${baseURL.pathname}/`
  ))
    return false;
  return true;
}
const sveltekitCookies = (getRequestEvent) => {
  return {
    id: "sveltekit-cookies",
    hooks: {
      after: [
        {
          matcher() {
            return true;
          },
          handler: createAuthMiddleware(async (ctx) => {
            const returned = ctx.context.responseHeaders;
            if ("_flag" in ctx && ctx._flag === "router") {
              return;
            }
            if (returned instanceof Headers) {
              const setCookies = returned?.get("set-cookie");
              if (!setCookies) return;
              const event = getRequestEvent();
              if (!event) return;
              const parsed = parseSetCookieHeader(setCookies);
              for (const [name, { value, ...ops }] of parsed) {
                try {
                  event.cookies.set(name, decodeURIComponent(value), {
                    sameSite: ops.samesite,
                    path: ops.path || "/",
                    expires: ops.expires,
                    secure: ops.secure,
                    httpOnly: ops.httponly,
                    domain: ops.domain,
                    maxAge: ops["max-age"]
                  });
                } catch (e) {
                }
              }
            }
          })
        }
      ]
    }
  };
};

export { isAuthPath, svelteKitHandler, sveltekitCookies, toSvelteKitHandler };
