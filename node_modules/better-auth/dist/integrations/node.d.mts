import * as http from 'http';
import { IncomingHttpHeaders } from 'http';
import { c as Auth } from '../shared/better-auth.-1DAdYNj.mjs';
import 'zod';
import 'kysely';
import 'better-call';
import '../shared/better-auth.DTtXpZYr.mjs';
import '../shared/better-auth.BHPr8J54.mjs';
import 'zod/v4/core';
import 'better-sqlite3';
import 'bun:sqlite';
import 'node:sqlite';

declare const toNodeHandler: (auth: {
    handler: Auth["handler"];
} | Auth["handler"]) => (req: http.IncomingMessage, res: http.ServerResponse) => Promise<void>;
declare function fromNodeHeaders(nodeHeaders: IncomingHttpHeaders): Headers;

export { fromNodeHeaders, toNodeHandler };
