import * as better_call from 'better-call';
import { H as HookEndpointContext } from '../shared/better-auth.C8uxRGim.js';
import 'zod';
import 'kysely';
import '../shared/better-auth.DTtXpZYr.js';
import '../shared/better-auth.C6qXK08w.js';
import 'zod/v4/core';
import 'better-sqlite3';
import 'bun:sqlite';
import 'node:sqlite';

declare const reactStartCookies: () => {
    id: "react-start-cookies";
    hooks: {
        after: {
            matcher(ctx: HookEndpointContext): true;
            handler: (inputContext: better_call.MiddlewareInputContext<better_call.MiddlewareOptions>) => Promise<void>;
        }[];
    };
};

export { reactStartCookies };
