import * as http from 'http';
import { IncomingHttpHeaders } from 'http';
import { c as Auth } from '../shared/better-auth.C8uxRGim.js';
import 'zod';
import 'kysely';
import 'better-call';
import '../shared/better-auth.DTtXpZYr.js';
import '../shared/better-auth.C6qXK08w.js';
import 'zod/v4/core';
import 'better-sqlite3';
import 'bun:sqlite';
import 'node:sqlite';

declare const toNodeHandler: (auth: {
    handler: Auth["handler"];
} | Auth["handler"]) => (req: http.IncomingMessage, res: http.ServerResponse) => Promise<void>;
declare function fromNodeHeaders(nodeHeaders: IncomingHttpHeaders): Headers;

export { fromNodeHeaders, toNodeHandler };
