import * as z from 'zod';
import { a as createAuthEndpoint, g as getSessionFromCtx, B as BASE_ERROR_CODES } from '../../shared/better-auth.CBY7cUGy.mjs';
import { APIError } from 'better-call';
import { m as mergeSchema } from '../../shared/better-auth.Dlx1jCB2.mjs';
import { g as generateRandomString } from '../../shared/better-auth.B4Qoxdgc.mjs';
import '../../shared/better-auth.BPua9lvR.mjs';
import { s as setSessionCookie } from '../../shared/better-auth.fV_ra52g.mjs';
import '../../shared/better-auth.CiuwFiHM.mjs';
import '../../shared/better-auth.C4g12FAA.mjs';
import { g as getDate } from '../../shared/better-auth.CW6D9eSx.mjs';
import '../../shared/better-auth.CvNZNAOW.mjs';
import '@better-auth/utils/hmac';
import '@better-auth/utils/base64';
import '@better-auth/utils/binary';
import '@better-auth/utils/random';
import '@better-auth/utils/hash';
import '../../crypto/index.mjs';
import '@noble/ciphers/chacha.js';
import '@noble/ciphers/utils.js';
import 'jose';
import '@noble/hashes/scrypt.js';
import '@better-auth/utils/hex';
import '@noble/hashes/utils.js';
import '../../shared/better-auth.DdzSJf-n.mjs';
import '@better-fetch/fetch';
import '../../shared/better-auth.DxV4YGX3.mjs';
import 'jose/errors';
import '../../shared/better-auth.BUPPRXfK.mjs';
import 'defu';

const ERROR_CODES = {
  INVALID_PHONE_NUMBER: "Invalid phone number",
  PHONE_NUMBER_EXIST: "Phone number already exists",
  INVALID_PHONE_NUMBER_OR_PASSWORD: "Invalid phone number or password",
  UNEXPECTED_ERROR: "Unexpected error",
  OTP_NOT_FOUND: "OTP not found",
  OTP_EXPIRED: "OTP expired",
  INVALID_OTP: "Invalid OTP",
  PHONE_NUMBER_NOT_VERIFIED: "Phone number not verified"
};

function generateOTP(size) {
  return generateRandomString(size, "0-9");
}
const phoneNumber = (options) => {
  const opts = {
    expiresIn: options?.expiresIn || 300,
    otpLength: options?.otpLength || 6,
    ...options,
    phoneNumber: "phoneNumber",
    phoneNumberVerified: "phoneNumberVerified",
    code: "code",
    createdAt: "createdAt"
  };
  return {
    id: "phone-number",
    endpoints: {
      /**
       * ### Endpoint
       *
       * POST `/sign-in/phone-number`
       *
       * ### API Methods
       *
       * **server:**
       * `auth.api.signInPhoneNumber`
       *
       * **client:**
       * `authClient.signIn.phoneNumber`
       *
       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/phone-number#api-method-sign-in-phone-number)
       */
      signInPhoneNumber: createAuthEndpoint(
        "/sign-in/phone-number",
        {
          method: "POST",
          body: z.object({
            phoneNumber: z.string().meta({
              description: 'Phone number to sign in. Eg: "+**********"'
            }),
            password: z.string().meta({
              description: "Password to use for sign in."
            }),
            rememberMe: z.boolean().meta({
              description: "Remember the session. Eg: true"
            }).optional()
          }),
          metadata: {
            openapi: {
              summary: "Sign in with phone number",
              description: "Use this endpoint to sign in with phone number",
              responses: {
                200: {
                  description: "Success",
                  content: {
                    "application/json": {
                      schema: {
                        type: "object",
                        properties: {
                          user: {
                            $ref: "#/components/schemas/User"
                          },
                          session: {
                            $ref: "#/components/schemas/Session"
                          }
                        }
                      }
                    }
                  }
                },
                400: {
                  description: "Invalid phone number or password"
                }
              }
            }
          }
        },
        async (ctx) => {
          const { password, phoneNumber: phoneNumber2 } = ctx.body;
          if (opts.phoneNumberValidator) {
            const isValidNumber = await opts.phoneNumberValidator(
              ctx.body.phoneNumber
            );
            if (!isValidNumber) {
              throw new APIError("BAD_REQUEST", {
                message: ERROR_CODES.INVALID_PHONE_NUMBER
              });
            }
          }
          const user = await ctx.context.adapter.findOne({
            model: "user",
            where: [
              {
                field: "phoneNumber",
                value: phoneNumber2
              }
            ]
          });
          if (!user) {
            throw new APIError("UNAUTHORIZED", {
              message: ERROR_CODES.INVALID_PHONE_NUMBER_OR_PASSWORD
            });
          }
          if (opts.requireVerification) {
            if (!user.phoneNumberVerified) {
              const otp = generateOTP(opts.otpLength);
              await ctx.context.internalAdapter.createVerificationValue(
                {
                  value: otp,
                  identifier: phoneNumber2,
                  expiresAt: getDate(opts.expiresIn, "sec")
                },
                ctx
              );
              await opts.sendOTP?.(
                {
                  phoneNumber: phoneNumber2,
                  code: otp
                },
                ctx.request
              );
              throw new APIError("UNAUTHORIZED", {
                message: ERROR_CODES.PHONE_NUMBER_NOT_VERIFIED
              });
            }
          }
          const accounts = await ctx.context.internalAdapter.findAccountByUserId(user.id);
          const credentialAccount = accounts.find(
            (a) => a.providerId === "credential"
          );
          if (!credentialAccount) {
            ctx.context.logger.error("Credential account not found", {
              phoneNumber: phoneNumber2
            });
            throw new APIError("UNAUTHORIZED", {
              message: ERROR_CODES.INVALID_PHONE_NUMBER_OR_PASSWORD
            });
          }
          const currentPassword = credentialAccount?.password;
          if (!currentPassword) {
            ctx.context.logger.error("Password not found", { phoneNumber: phoneNumber2 });
            throw new APIError("UNAUTHORIZED", {
              message: ERROR_CODES.UNEXPECTED_ERROR
            });
          }
          const validPassword = await ctx.context.password.verify({
            hash: currentPassword,
            password
          });
          if (!validPassword) {
            ctx.context.logger.error("Invalid password");
            throw new APIError("UNAUTHORIZED", {
              message: ERROR_CODES.INVALID_PHONE_NUMBER_OR_PASSWORD
            });
          }
          const session = await ctx.context.internalAdapter.createSession(
            user.id,
            ctx,
            ctx.body.rememberMe === false
          );
          if (!session) {
            ctx.context.logger.error("Failed to create session");
            throw new APIError("UNAUTHORIZED", {
              message: BASE_ERROR_CODES.FAILED_TO_CREATE_SESSION
            });
          }
          await setSessionCookie(
            ctx,
            {
              session,
              user
            },
            ctx.body.rememberMe === false
          );
          return ctx.json({
            token: session.token,
            user: {
              id: user.id,
              email: user.email,
              emailVerified: user.emailVerified,
              name: user.name,
              image: user.image,
              phoneNumber: user.phoneNumber,
              phoneNumberVerified: user.phoneNumberVerified,
              createdAt: user.createdAt,
              updatedAt: user.updatedAt
            }
          });
        }
      ),
      /**
       * ### Endpoint
       *
       * POST `/phone-number/send-otp`
       *
       * ### API Methods
       *
       * **server:**
       * `auth.api.sendPhoneNumberOTP`
       *
       * **client:**
       * `authClient.phoneNumber.sendOtp`
       *
       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/phone-number#api-method-phone-number-send-otp)
       */
      sendPhoneNumberOTP: createAuthEndpoint(
        "/phone-number/send-otp",
        {
          method: "POST",
          body: z.object({
            phoneNumber: z.string().meta({
              description: 'Phone number to send OTP. Eg: "+**********"'
            })
          }),
          metadata: {
            openapi: {
              summary: "Send OTP to phone number",
              description: "Use this endpoint to send OTP to phone number",
              responses: {
                200: {
                  description: "Success",
                  content: {
                    "application/json": {
                      schema: {
                        type: "object",
                        properties: {
                          message: {
                            type: "string"
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        async (ctx) => {
          if (!options?.sendOTP) {
            ctx.context.logger.warn("sendOTP not implemented");
            throw new APIError("NOT_IMPLEMENTED", {
              message: "sendOTP not implemented"
            });
          }
          if (opts.phoneNumberValidator) {
            const isValidNumber = await opts.phoneNumberValidator(
              ctx.body.phoneNumber
            );
            if (!isValidNumber) {
              throw new APIError("BAD_REQUEST", {
                message: ERROR_CODES.INVALID_PHONE_NUMBER
              });
            }
          }
          const code = generateOTP(opts.otpLength);
          await ctx.context.internalAdapter.createVerificationValue(
            {
              value: `${code}:0`,
              identifier: ctx.body.phoneNumber,
              expiresAt: getDate(opts.expiresIn, "sec")
            },
            ctx
          );
          await options.sendOTP(
            {
              phoneNumber: ctx.body.phoneNumber,
              code
            },
            ctx.request
          );
          return ctx.json({ message: "code sent" });
        }
      ),
      /**
       * ### Endpoint
       *
       * POST `/phone-number/verify`
       *
       * ### API Methods
       *
       * **server:**
       * `auth.api.verifyPhoneNumber`
       *
       * **client:**
       * `authClient.phoneNumber.verify`
       *
       * @see [Read our docs to learn more.](https://better-auth.com/docs/plugins/phone-number#api-method-phone-number-verify)
       */
      verifyPhoneNumber: createAuthEndpoint(
        "/phone-number/verify",
        {
          method: "POST",
          body: z.object({
            /**
             * Phone number
             */
            phoneNumber: z.string().meta({
              description: 'Phone number to verify. Eg: "+**********"'
            }),
            /**
             * OTP code
             */
            code: z.string().meta({
              description: 'OTP code. Eg: "123456"'
            }),
            /**
             * Disable session creation after verification
             * @default false
             */
            disableSession: z.boolean().meta({
              description: "Disable session creation after verification. Eg: false"
            }).optional(),
            /**
             * This checks if there is a session already
             * and updates the phone number with the provided
             * phone number
             */
            updatePhoneNumber: z.boolean().meta({
              description: "Check if there is a session and update the phone number. Eg: true"
            }).optional()
          }),
          metadata: {
            openapi: {
              summary: "Verify phone number",
              description: "Use this endpoint to verify phone number",
              responses: {
                "200": {
                  description: "Phone number verified successfully",
                  content: {
                    "application/json": {
                      schema: {
                        type: "object",
                        properties: {
                          status: {
                            type: "boolean",
                            description: "Indicates if the verification was successful",
                            enum: [true]
                          },
                          token: {
                            type: "string",
                            nullable: true,
                            description: "Session token if session is created, null if disableSession is true or no session is created"
                          },
                          user: {
                            type: "object",
                            nullable: true,
                            properties: {
                              id: {
                                type: "string",
                                description: "Unique identifier of the user"
                              },
                              email: {
                                type: "string",
                                format: "email",
                                nullable: true,
                                description: "User's email address"
                              },
                              emailVerified: {
                                type: "boolean",
                                nullable: true,
                                description: "Whether the email is verified"
                              },
                              name: {
                                type: "string",
                                nullable: true,
                                description: "User's name"
                              },
                              image: {
                                type: "string",
                                format: "uri",
                                nullable: true,
                                description: "User's profile image URL"
                              },
                              phoneNumber: {
                                type: "string",
                                description: "User's phone number"
                              },
                              phoneNumberVerified: {
                                type: "boolean",
                                description: "Whether the phone number is verified"
                              },
                              createdAt: {
                                type: "string",
                                format: "date-time",
                                description: "Timestamp when the user was created"
                              },
                              updatedAt: {
                                type: "string",
                                format: "date-time",
                                description: "Timestamp when the user was last updated"
                              }
                            },
                            required: [
                              "id",
                              "phoneNumber",
                              "phoneNumberVerified",
                              "createdAt",
                              "updatedAt"
                            ],
                            description: "User object with phone number details, null if no user is created or found"
                          }
                        },
                        required: ["status"]
                      }
                    }
                  }
                },
                400: {
                  description: "Invalid OTP"
                }
              }
            }
          }
        },
        async (ctx) => {
          const otp = await ctx.context.internalAdapter.findVerificationValue(
            ctx.body.phoneNumber
          );
          if (!otp || otp.expiresAt < /* @__PURE__ */ new Date()) {
            if (otp && otp.expiresAt < /* @__PURE__ */ new Date()) {
              throw new APIError("BAD_REQUEST", {
                message: "OTP expired"
              });
            }
            throw new APIError("BAD_REQUEST", {
              message: ERROR_CODES.OTP_NOT_FOUND
            });
          }
          const [otpValue, attempts] = otp.value.split(":");
          const allowedAttempts = options?.allowedAttempts || 3;
          if (attempts && parseInt(attempts) >= allowedAttempts) {
            await ctx.context.internalAdapter.deleteVerificationValue(otp.id);
            throw new APIError("FORBIDDEN", {
              message: "Too many attempts"
            });
          }
          if (otpValue !== ctx.body.code) {
            await ctx.context.internalAdapter.updateVerificationValue(otp.id, {
              value: `${otpValue}:${parseInt(attempts || "0") + 1}`
            });
            throw new APIError("BAD_REQUEST", {
              message: "Invalid OTP"
            });
          }
          await ctx.context.internalAdapter.deleteVerificationValue(otp.id);
          if (ctx.body.updatePhoneNumber) {
            const session = await getSessionFromCtx(ctx);
            if (!session) {
              throw new APIError("UNAUTHORIZED", {
                message: BASE_ERROR_CODES.USER_NOT_FOUND
              });
            }
            const existingUser = await ctx.context.adapter.findMany({
              model: "user",
              where: [
                {
                  field: "phoneNumber",
                  value: ctx.body.phoneNumber
                }
              ]
            });
            if (existingUser.length) {
              throw ctx.error("BAD_REQUEST", {
                message: ERROR_CODES.PHONE_NUMBER_EXIST
              });
            }
            let user2 = await ctx.context.internalAdapter.updateUser(
              session.user.id,
              {
                [opts.phoneNumber]: ctx.body.phoneNumber,
                [opts.phoneNumberVerified]: true
              },
              ctx
            );
            return ctx.json({
              status: true,
              token: session.session.token,
              user: {
                id: user2.id,
                email: user2.email,
                emailVerified: user2.emailVerified,
                name: user2.name,
                image: user2.image,
                phoneNumber: user2.phoneNumber,
                phoneNumberVerified: user2.phoneNumberVerified,
                createdAt: user2.createdAt,
                updatedAt: user2.updatedAt
              }
            });
          }
          let user = await ctx.context.adapter.findOne({
            model: "user",
            where: [
              {
                value: ctx.body.phoneNumber,
                field: opts.phoneNumber
              }
            ]
          });
          if (!user) {
            if (options?.signUpOnVerification) {
              user = await ctx.context.internalAdapter.createUser(
                {
                  email: options.signUpOnVerification.getTempEmail(
                    ctx.body.phoneNumber
                  ),
                  name: options.signUpOnVerification.getTempName ? options.signUpOnVerification.getTempName(
                    ctx.body.phoneNumber
                  ) : ctx.body.phoneNumber,
                  [opts.phoneNumber]: ctx.body.phoneNumber,
                  [opts.phoneNumberVerified]: true
                },
                ctx
              );
              if (!user) {
                throw new APIError("INTERNAL_SERVER_ERROR", {
                  message: BASE_ERROR_CODES.FAILED_TO_CREATE_USER
                });
              }
            }
          } else {
            user = await ctx.context.internalAdapter.updateUser(
              user.id,
              {
                [opts.phoneNumberVerified]: true
              },
              ctx
            );
          }
          if (!user) {
            throw new APIError("INTERNAL_SERVER_ERROR", {
              message: BASE_ERROR_CODES.FAILED_TO_UPDATE_USER
            });
          }
          await options?.callbackOnVerification?.(
            {
              phoneNumber: ctx.body.phoneNumber,
              user
            },
            ctx.request
          );
          if (!ctx.body.disableSession) {
            const session = await ctx.context.internalAdapter.createSession(
              user.id,
              ctx
            );
            if (!session) {
              throw new APIError("INTERNAL_SERVER_ERROR", {
                message: BASE_ERROR_CODES.FAILED_TO_CREATE_SESSION
              });
            }
            await setSessionCookie(ctx, {
              session,
              user
            });
            return ctx.json({
              status: true,
              token: session.token,
              user: {
                id: user.id,
                email: user.email,
                emailVerified: user.emailVerified,
                name: user.name,
                image: user.image,
                phoneNumber: user.phoneNumber,
                phoneNumberVerified: user.phoneNumberVerified,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
              }
            });
          }
          return ctx.json({
            status: true,
            token: null,
            user: {
              id: user.id,
              email: user.email,
              emailVerified: user.emailVerified,
              name: user.name,
              image: user.image,
              phoneNumber: user.phoneNumber,
              phoneNumberVerified: user.phoneNumberVerified,
              createdAt: user.createdAt,
              updatedAt: user.updatedAt
            }
          });
        }
      ),
      /**
       * @deprecated Use requestPasswordResetPhoneNumber instead. This endpoint will be removed in the next major version.
       */
      forgetPasswordPhoneNumber: createAuthEndpoint(
        "/phone-number/forget-password",
        {
          method: "POST",
          body: z.object({
            phoneNumber: z.string().meta({
              description: `The phone number which is associated with the user. Eg: "+**********"`
            })
          }),
          metadata: {
            openapi: {
              description: "Request OTP for password reset via phone number",
              responses: {
                "200": {
                  description: "OTP sent successfully for password reset",
                  content: {
                    "application/json": {
                      schema: {
                        type: "object",
                        properties: {
                          status: {
                            type: "boolean",
                            description: "Indicates if the OTP was sent successfully",
                            enum: [true]
                          }
                        },
                        required: ["status"]
                      }
                    }
                  }
                }
              }
            }
          }
        },
        async (ctx) => {
          const user = await ctx.context.adapter.findOne({
            model: "user",
            where: [
              {
                value: ctx.body.phoneNumber,
                field: opts.phoneNumber
              }
            ]
          });
          if (!user) {
            throw new APIError("BAD_REQUEST", {
              message: "phone number isn't registered"
            });
          }
          const code = generateOTP(opts.otpLength);
          await ctx.context.internalAdapter.createVerificationValue(
            {
              value: `${code}:0`,
              identifier: `${ctx.body.phoneNumber}-request-password-reset`,
              expiresAt: getDate(opts.expiresIn, "sec")
            },
            ctx
          );
          await options?.sendForgetPasswordOTP?.(
            {
              phoneNumber: ctx.body.phoneNumber,
              code
            },
            ctx.request
          );
          return ctx.json({
            status: true
          });
        }
      ),
      requestPasswordResetPhoneNumber: createAuthEndpoint(
        "/phone-number/request-password-reset",
        {
          method: "POST",
          body: z.object({
            phoneNumber: z.string()
          }),
          metadata: {
            openapi: {
              description: "Request OTP for password reset via phone number",
              responses: {
                "200": {
                  description: "OTP sent successfully for password reset",
                  content: {
                    "application/json": {
                      schema: {
                        type: "object",
                        properties: {
                          status: {
                            type: "boolean",
                            description: "Indicates if the OTP was sent successfully",
                            enum: [true]
                          }
                        },
                        required: ["status"]
                      }
                    }
                  }
                }
              }
            }
          }
        },
        async (ctx) => {
          const user = await ctx.context.adapter.findOne({
            model: "user",
            where: [
              {
                value: ctx.body.phoneNumber,
                field: opts.phoneNumber
              }
            ]
          });
          if (!user) {
            throw new APIError("BAD_REQUEST", {
              message: "phone number isn't registered"
            });
          }
          const code = generateOTP(opts.otpLength);
          await ctx.context.internalAdapter.createVerificationValue(
            {
              value: `${code}:0`,
              identifier: `${ctx.body.phoneNumber}-request-password-reset`,
              expiresAt: getDate(opts.expiresIn, "sec")
            },
            ctx
          );
          await options?.sendPasswordResetOTP?.(
            {
              phoneNumber: ctx.body.phoneNumber,
              code
            },
            ctx.request
          );
          return ctx.json({
            status: true
          });
        }
      ),
      resetPasswordPhoneNumber: createAuthEndpoint(
        "/phone-number/reset-password",
        {
          method: "POST",
          body: z.object({
            otp: z.string().meta({
              description: 'The one time password to reset the password. Eg: "123456"'
            }),
            phoneNumber: z.string().meta({
              description: 'The phone number to the account which intends to reset the password for. Eg: "+**********"'
            }),
            newPassword: z.string().meta({
              description: `The new password. Eg: "new-and-secure-password"`
            })
          }),
          metadata: {
            openapi: {
              description: "Reset password using phone number OTP",
              responses: {
                "200": {
                  description: "Password reset successfully",
                  content: {
                    "application/json": {
                      schema: {
                        type: "object",
                        properties: {
                          status: {
                            type: "boolean",
                            description: "Indicates if the password was reset successfully",
                            enum: [true]
                          }
                        },
                        required: ["status"]
                      }
                    }
                  }
                }
              }
            }
          }
        },
        async (ctx) => {
          const verification = await ctx.context.internalAdapter.findVerificationValue(
            `${ctx.body.phoneNumber}-request-password-reset`
          );
          if (!verification) {
            throw new APIError("BAD_REQUEST", {
              message: ERROR_CODES.OTP_NOT_FOUND
            });
          }
          if (verification.expiresAt < /* @__PURE__ */ new Date()) {
            throw new APIError("BAD_REQUEST", {
              message: ERROR_CODES.OTP_EXPIRED
            });
          }
          const [otpValue, attempts] = verification.value.split(":");
          const allowedAttempts = options?.allowedAttempts || 3;
          if (attempts && parseInt(attempts) >= allowedAttempts) {
            await ctx.context.internalAdapter.deleteVerificationValue(
              verification.id
            );
            throw new APIError("FORBIDDEN", {
              message: "Too many attempts"
            });
          }
          if (ctx.body.otp !== otpValue) {
            await ctx.context.internalAdapter.updateVerificationValue(
              verification.id,
              {
                value: `${otpValue}:${parseInt(attempts || "0") + 1}`
              }
            );
            throw new APIError("BAD_REQUEST", {
              message: ERROR_CODES.INVALID_OTP
            });
          }
          const user = await ctx.context.adapter.findOne({
            model: "user",
            where: [
              {
                field: "phoneNumber",
                value: ctx.body.phoneNumber
              }
            ]
          });
          if (!user) {
            throw new APIError("BAD_REQUEST", {
              message: ERROR_CODES.UNEXPECTED_ERROR
            });
          }
          const hashedPassword = await ctx.context.password.hash(
            ctx.body.newPassword
          );
          await ctx.context.internalAdapter.updatePassword(
            user.id,
            hashedPassword
          );
          await ctx.context.internalAdapter.deleteVerificationValue(
            verification.id
          );
          return ctx.json({
            status: true
          });
        }
      )
    },
    schema: mergeSchema(schema, options?.schema),
    rateLimit: [
      {
        pathMatcher(path) {
          return path.startsWith("/phone-number");
        },
        window: 60 * 1e3,
        max: 10
      }
    ],
    $ERROR_CODES: ERROR_CODES
  };
};
const schema = {
  user: {
    fields: {
      phoneNumber: {
        type: "string",
        required: false,
        unique: true,
        sortable: true,
        returned: true
      },
      phoneNumberVerified: {
        type: "boolean",
        required: false,
        returned: true,
        input: false
      }
    }
  }
};

export { phoneNumber };
