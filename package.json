{"name": "sales-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "cf-typegen": "wrangler types --env-interface CloudflareEnv ./cloudflare-env.d.ts", "db:push": "drizzle-kit push", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:seed": "drizzle-kit seed", "db:studio": "drizzle-kit studio"}, "dependencies": {"@neondatabase/serverless": "^1.0.1", "@opennextjs/cloudflare": "^1.3.0", "better-auth": "^1.3.12", "dotenv": "^17.2.2", "drizzle-orm": "^0.44.5", "next": "15.4.6", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.17", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "tsx": "^4.20.5", "typescript": "^5", "wrangler": "^4.38.0"}}